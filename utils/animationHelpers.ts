/**
 * 动画辅助工具函数
 * 提供性能监控、错误处理和调试功能
 */

// 动画性能监控
export const createAnimationMonitor = () => {
  let animationStartTime: number;
  
  return {
    start: (animationName: string) => {
      animationStartTime = performance.now();
      console.log(`🎬 Animation started: ${animationName}`);
    },
    
    end: (animationName: string) => {
      const duration = performance.now() - animationStartTime;
      console.log(`✅ Animation completed: ${animationName} (${duration.toFixed(2)}ms)`);
      
      // 性能警告
      if (duration > 500) {
        console.warn(`⚠️ Slow animation detected: ${animationName} took ${duration.toFixed(2)}ms`);
      }
    }
  };
};

// 检测用户设备性能
export const getDevicePerformance = () => {
  // 简单的设备性能检测
  const connection = (navigator as any).connection;
  const hardwareConcurrency = navigator.hardwareConcurrency || 4;
  
  return {
    isLowEnd: hardwareConcurrency <= 2,
    isSlowConnection: connection?.effectiveType === '2g' || connection?.effectiveType === 'slow-2g',
    shouldReduceAnimations: hardwareConcurrency <= 2 || (connection?.effectiveType === '2g')
  };
};

// 动画错误边界
export const createAnimationErrorBoundary = (fallbackComponent: React.ComponentType) => {
  return class AnimationErrorBoundary extends React.Component {
    constructor(props: any) {
      super(props);
      this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error) {
      console.error('Animation error:', error);
      return { hasError: true };
    }

    componentDidCatch(error: Error, errorInfo: any) {
      console.error('Animation error details:', error, errorInfo);
    }

    render() {
      if ((this.state as any).hasError) {
        return React.createElement(fallbackComponent);
      }

      return (this.props as any).children;
    }
  };
};

// 动画调试工具
export const debugAnimation = (name: string, variants: any) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔍 Animation Debug - ${name}:`, variants);
  }
};
