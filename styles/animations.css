/**
 * 动画性能优化样式
 * 确保 GPU 加速和流畅的动画体验
 */

/* 为动画元素启用 GPU 加速 */
.motion-element {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .motion-element {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .motion-element {
    filter: contrast(1.2);
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .motion-element {
    transform: translate3d(0, 0, 0);
  }
}
